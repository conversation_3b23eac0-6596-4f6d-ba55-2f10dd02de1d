#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import "LeisurelandTycoonController.h"
#import "NetworkUtil.h"
#import "ThinkingDataWrapper.h"
#import "AIHelpWrapper.h"
#import "WrapperExtern.h"
#import "ApplePay.h"
#import "FirebaseWrapper.h"
#import "FacebookWrapper.h"
#import "AdjustWrapper.h"

UIApplication* uiApp = NULL;

@implementation LeisurelandTycoonController

- (BOOL)application:(UIApplication*)application didFinishLaunchingWithOptions:(NSDictionary*)launchOptions
{
    [super application:application didFinishLaunchingWithOptions:launchOptions];
    uiApp = application;
    
    //Firebase
    [[FirebaseWrapper sharedInstance] initFirebase];

    //facebook
    [[FacebookWrapper sharedInstance] initFacebook:launchOptions];

    //支付监听
    [[ApplePay sharedInstance] initApplePay];

    //Adjust
    [[AdjustWrapper sharedInstance] initAdjust:launchOptions];

	// 启动网络状态监听
    [[NetworkUtil sharedInstance] startMonitoring];

    //数数sdk
    [[ThinkingDataWrapper sharedInstance] initialize];

    //aihlep sdk
    [[AIHelpWrapper sharedInstance] initialize];
    return YES;
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    [super applicationDidBecomeActive:application];
    // 应用激活时的逻辑处理
    if (application.applicationIconBadgeNumber > 0) {
        application.applicationIconBadgeNumber = 0;
    }
}

// 处理通过URL打开应用
- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    BOOL handledByFacebook = [[FBSDKApplicationDelegate sharedInstance] application:app
                                                                  openURL:url
                                                        sourceApplication:options[UIApplicationOpenURLOptionsSourceApplicationKey]
                                                               annotation:options[UIApplicationOpenURLOptionsAnnotationKey]];

    return handledByFacebook;
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray *restorableObjects))restorationHandler {

    if ([[userActivity activityType] isEqualToString:NSUserActivityTypeBrowsingWeb]) {
        NSURL *url = [userActivity webpageURL];
        // url object contains your universal link content
        NSLog(@"SDK.application continueUserActivity %@", url);
        //接受深度链接
        [[AdjustWrapper sharedInstance] handleURL:url];
    }

    return YES;
}
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler 
     {
         // 添加“哨兵”日志
         NSLog(@"[SDK.application] 'didReceiveRemoteNotification'");

         // 然后再转发给 FirebaseWrapper
         [[FirebaseWrapper sharedInstance] application:application 
                      didFirebaseReceiveRemoteNotification:userInfo 
                            fetchCompletionHandler:completionHandler];
     }
     // 这是处理“注册成功”的方法
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    NSLog(@"[SDK.application] didFirebaseRegisterForRemoteNotificationsWithDeviceToken. Forwarding to FirebaseWrapper...");

    [[FirebaseWrapper sharedInstance] application:application didFirebaseRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}

// 这是处理“注册失败”的方法
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
    NSLog(@"[SDK.application] didFirebaseFailToRegisterForRemoteNotificationsWithError. Forwarding to FirebaseWrapper...");
    [[FirebaseWrapper sharedInstance] application:application didFirebaseFailToRegisterForRemoteNotificationsWithError:error];
}
@end

IMPL_APP_CONTROLLER_SUBCLASS(LeisurelandTycoonController)
