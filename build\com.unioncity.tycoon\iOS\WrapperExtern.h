#import "Utils.h"
#import "ThinkingDataWrapper.h"
#import "AppleLogin.h"
#import "ApplePay.h"
#import "KeyChainUtil.h"
#import "AppleTrackingAuthorization.h"
#import "HapticWrapper.h"
#import "FirebaseWrapper.h"
#import "FacebookWrapper.h"
#import "AdjustWrapper.h"
#import "AppReviewWrapper.h"

#ifdef __cplusplus
extern "C" {
#endif
    //Apple Login
    void iOSAppleLogin() {
        [[AppleLogin sharedInstance] loginWithAppleId];
    }

    //Apple Pay
    void iOSLoadSkuDetails(const char *inappSkus) {
        [[ApplePay sharedInstance] LoadSkuDetails:[NSString stringWithUTF8String:inappSkus]];
    }

    void iOSPurchase(const char *sku, const char *extraData) {
        [[ApplePay sharedInstance] Purchase:[NSString stringWithUTF8String:sku] extraData:[NSString stringWithUTF8String:extraData]];
    }

    void iOSFinishTransaction(const char *transactionId) {
        [[ApplePay sharedInstance] FinishTransaction:[NSString stringWithUTF8String:transactionId]];
    }

    //Apple ATT
    void iOSRequestTrackingAuthorization() {
        [AppleTrackingAuthorization requestTrackingAuthorization];
    }

    //Thinking SDK
    void iOSThinkingDataSetEnvironment(BOOL isDebug)
    {
        [[ThinkingDataWrapper sharedInstance] ThinkingDataSetEnvironment:isDebug];
    }

    void iOSThinkingDataLogin(const char* userId)
    {
        [[ThinkingDataWrapper sharedInstance] ThinkingDataLogin:[NSString stringWithUTF8String:userId]];
    }
    
    void iOSThinkingDataSetSuperProperty(const char* property)
    {
        [[ThinkingDataWrapper sharedInstance] ThinkingDataSetSuperProperty:[NSString stringWithUTF8String:property]];
    }

    void iOSThinkingDataSetSuperPropertyDynamic(const char* property)
    {
        [[ThinkingDataWrapper sharedInstance] ThinkingDataSetSuperPropertyDynamic:[NSString stringWithUTF8String:property]];
    }

    void iOSThinkingDataSetUserProperties(const char* property)
    {
        [[ThinkingDataWrapper sharedInstance] ThinkingDataSetUserProperties:[NSString stringWithUTF8String:property]];
    }

    void iOSThinkingDataSetUserOnceProperties(const char* property)
    {
        [[ThinkingDataWrapper sharedInstance] ThinkingDataSetUserOnceProperties:[NSString stringWithUTF8String:property]];
    }

    void iOSThinkingDataTrack(const char* key, const char* property)
    {
        [[ThinkingDataWrapper sharedInstance] ThinkingDataTrack:[NSString stringWithUTF8String:key] property:[NSString stringWithUTF8String:property]];
    }

    void iOSThinkingDataCalibrateTime(long ms)
    {
        [[ThinkingDataWrapper sharedInstance] ThinkingDataCalibrateTime:ms];
    }

    //KeyChain
    void iOSSetKeyChainData(const char* data)
    {
        [[StorageUtil sharedInstance] setKeyChain:[NSString stringWithUTF8String:data]];
    }

    const char* iOSGetKeyChainData()
    {
        NSString *dataString = [[StorageUtil sharedInstance] getKeyChain];
        if (dataString != nil) {
            const char *utf8String = [dataString UTF8String];
            char *resultString = strdup(utf8String); // 创建字符串的副本
            return resultString; // 返回副本指针
        } else {
            return NULL;
        }
    }

    //AIHelp
    void iOSAIHelpUpdateLanguage(const char* data)
    {
        [[AIHelpWrapper sharedInstance] AIHelpUpdateLanguage:[NSString stringWithUTF8String:data]];
    }

    void iOSAIHelpUpdateUserInfo(const char* data)
    {
        [[AIHelpWrapper sharedInstance] AIHelpUpdateUserInfo:[NSString stringWithUTF8String:data]];
    }

    void iOSAIHelpOpenView(const char* data)
    {
        NSString *viewData = data ? [NSString stringWithUTF8String:data] : @"E001";
        [[AIHelpWrapper sharedInstance] AIHelpOpenView:viewData];
    }

    void iOSAIHelpSetPushToken(const char* token)
    {
        [[AIHelpWrapper sharedInstance] AIHelpSetPushToken:[NSString stringWithUTF8String:token]];
    }

    //Firebase
    void iOSGetFCMToken()
    {
        [[FirebaseWrapper sharedInstance] getFCMToken];
    }

    void iOSFirebaseTrack(const char *key, const char *property)
    {
        [[FirebaseWrapper sharedInstance] logEvent:[NSString stringWithUTF8String:key] property:[NSString stringWithUTF8String:property]];
    }

    void iOSFirebaseRequestNotificationPermission()
    {
        [[FirebaseWrapper sharedInstance] registerForRemoteNotifications];
    }

    void iOSFirebaseSetQuietTime(int startHour, int startMinute, int endHour, int endMinute)
    {
        [[FirebaseWrapper sharedInstance] setQuietTime:startHour startMinute:startMinute endHour:endHour endMinute:endMinute];
    }
    void iOSFirebaseSendNoShield(const char *title, const char *body, const char * imageUrl) {
        NSString *titleStr = title ? [NSString stringWithUTF8String:title] : @"";
        NSString *bodyStr = body ? [NSString stringWithUTF8String:body] : @"";
        NSString *imageUrlStr = imageUrl ? [NSString stringWithUTF8String:imageUrl] : nil;
        
        [[FirebaseWrapper sharedInstance] displayNotificationWithTitle:titleStr body:bodyStr imageURL:imageUrlStr];
    }
	
	void iOSFirebaseAnalyticsCollectionEnabled()
	{
        [[FirebaseWrapper sharedInstance] setAnalyticsCollectionEnabled];
	}
	
    //Util
    const char* iOSGetSystemLanguage()
    {
        NSString *dataString = [Utils GetSystemLanguage];
        if (dataString != nil) {
            const char *utf8String = [dataString UTF8String];
            char *resultString = strdup(utf8String); // 创建字符串的副本
            return resultString; // 返回副本指针
        } else {
            return NULL;
        }
    }

    void iOSVibrator()
    {
        [Utils Vibrator];
    }

    void iOSVibrateWithDurations(int* durations, int durationsCount, int* amplitudes, int amplitudesCount, int delay)
    {
        // 版本限制：只有在 iOS 13.0 及以上版本支持震动
        if (@available(iOS 13.0, *)) {
            NSMutableArray<NSNumber *> *durationArray = [NSMutableArray arrayWithCapacity:durationsCount];
            NSMutableArray<NSNumber *> *amplitudeArray = [NSMutableArray arrayWithCapacity:amplitudesCount];

            for (int i = 0; i < durationsCount; i++) {
                [durationArray addObject:@(durations[i])];
            }

            for (int i = 0; i < amplitudesCount; i++) {
                [amplitudeArray addObject:@(amplitudes[i])];
            }

            [[HapticWrapper sharedInstance] VibrateWithDurations:durationArray amplitudes:amplitudeArray delay:delay];
        } else {
            NSLog(@"Haptic feedback is not supported on this iOS version.");
            [Utils Vibrator];
        }
    }

    void iOSCopyToClipboard(const char* text)
    {
        [Utils CopyToClipboard:[NSString stringWithUTF8String:text]];
    }

    void iOSOpenNotificationSettings()
    {
        [Utils OpenNotificationSettings];
    }

    void iOSCheckNotificationEnabled()
    {
        [Utils CheckNotificationEnabled];
    }

    void iOSInviteSystem(const char* data)
    {
        [Utils InviteSystem:[NSString stringWithUTF8String:data]];
    }

    long iOSGetAvailableSpace(const char *path) 
    {
        return [Utils GetAvailableSpace:[NSString stringWithUTF8String:path]];
    }

    long iOSGetTotalSpace(const char *path) 
    {
        return [Utils GetTotalSpace:[NSString stringWithUTF8String:path]];
    }

    long iOSGetUsedSpace(const char *path) 
    {
        return [Utils GetUsedSpace:[NSString stringWithUTF8String:path]];
    }

    void iOSOpenFansUrl(const char* data)
    {
        [Utils OpenFansUrl:[NSString stringWithUTF8String:data]];
    }

    void iOSOpenURL(const char* url)
    {
        [Utils OpenUrl:[NSString stringWithUTF8String:url]];
    }

    //Facebook
    void iOSFacebookLogin()
    {
        [[FacebookWrapper sharedInstance] FacebookLogin];
    }

    void iOSFacebookLoginWithPermissions(const char* permissions)
    {
        [[FacebookWrapper sharedInstance] FacebookLoginWithPermissions:[NSString stringWithUTF8String:permissions]];
    }

    void iOSFacebookLogout()
    {
        [[FacebookWrapper sharedInstance] FacebookLogout];
    }

    void iOSFacebookShare(const char* data)
    {
        [[FacebookWrapper sharedInstance] FacebookShare:[NSString stringWithUTF8String:data]];
    }

    void iOSFacebookTrack(const char *key, const char *property)
    {
        [[FacebookWrapper sharedInstance] FacebookTrack:[NSString stringWithUTF8String:key] property:[NSString stringWithUTF8String:property]];
    }

    void iOSFacebookSetDataProcessingOptions(int stateCode) {
        [[FacebookWrapper sharedInstance] FacebookSetDataProcessingOptions:stateCode];
    }
	
    //AppReview
    void iOSOpenReview()
    {
        [[AppReviewWrapper sharedInstance] requestReview];
    }

    //Adjust
     const char* iOSAdjustGetDeepLink() {
        NSString *deepLink = [[AdjustWrapper sharedInstance] getDeepLink];
        if (deepLink != nil) {
            const char *utf8String = [deepLink UTF8String];
            char *resultString = strdup(utf8String); // 创建字符串的副本
            return resultString; // 返回副本指针
        }
        return NULL;
    }

    const char* iOSAdjustGetAttribution() {
        NSString *attribution = [[AdjustWrapper sharedInstance] getAttribution];
        if (attribution != nil) {
            const char *utf8String = [attribution UTF8String];
            char *resultString = strdup(utf8String); // 创建字符串的副本
            return resultString; // 返回副本指针
        }
        return NULL;
    }

    const char* iOSAdjustGetAdId() {
        NSString *adId = [[AdjustWrapper sharedInstance] getAdjustAdId];
        if (adId != nil) {
            const char *utf8String = [adId UTF8String];
            char *resultString = strdup(utf8String); // 创建字符串的副本
            return resultString; // 返回副本指针
        }
        return NULL;
    }

    const char* iOSAdjustGetIdfa() {
        NSString *idfa = [[AdjustWrapper sharedInstance] getAdjustIdfa];
        if (idfa != nil) {
            const char *utf8String = [idfa UTF8String];
            char *resultString = strdup(utf8String); // 创建字符串的副本
            return resultString; // 返回副本指针
        }
        return NULL;
    }

    void iOSAdjustTrackEvent(const char *eventName) {
        NSString *eventNameStr = [NSString stringWithUTF8String:eventName];
        [[AdjustWrapper sharedInstance] trackEventWithEventName:eventNameStr];
    }

    void iOSAdjustTrackPayEvent(const char *eventName, const char *currency, double payValue) {
        NSString *eventNameStr = [NSString stringWithUTF8String:eventName];
        NSString *currencyStr = [NSString stringWithUTF8String:currency];
        
        [[AdjustWrapper sharedInstance] trackPayEventWithEventName:eventNameStr
                                                        currency:currencyStr
                                                        payValue:payValue];
    }

    void iOSAdjustTrackAdRevenue(double revenue, const char *currency, const char *networkName, const char *adUnitIdentifier, const char *placement) {
        NSString *currencyStr = [NSString stringWithUTF8String:currency];
        NSString *networkNameStr = [NSString stringWithUTF8String:networkName];
        NSString *adUnitIdentifierStr = [NSString stringWithUTF8String:adUnitIdentifier];
        NSString *placementStr = [NSString stringWithUTF8String:placement];
        
        [[AdjustWrapper sharedInstance] trackAdRevenueWithRevenue:revenue
                                                         currency:currencyStr
                                                      networkName:networkNameStr
                                                 adUnitIdentifier:adUnitIdentifierStr
                                                        placement:placementStr];
    }

    void iOSAdjustTrackFacebookSharing(int stateCode) {
        [[AdjustWrapper sharedInstance] trackFacebookSharing:stateCode];
    }

    void iOSAdjustTrackGdprSharing(int stateCode) {
        [[AdjustWrapper sharedInstance] trackGdprSharing:stateCode];
    }

    void iOSAdjustEndFirstSessionDelay() {
        [[AdjustWrapper sharedInstance] endFirstSessionDelay];
    }

    //init SDK（ATT授权后调用）
    
    void iOSInitSdk() {
        //[[AdjustWrapper sharedInstance] initSdk];
        //[[FacebookWrapper sharedInstance] initSdk];
    }

    // 新增：独立初始化 Adjust SDK
    void iOSAdjustInitSdk() {
        [[AdjustWrapper sharedInstance] initSdk];
    }

    // 新增：独立初始化 Facebook SDK (ATT授权后调用)
    void iOSFacebookInitSdk() {
        [[FacebookWrapper sharedInstance] initSdk];
    }

#ifdef __cplusplus
}
#endif