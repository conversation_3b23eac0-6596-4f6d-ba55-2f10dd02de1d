#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <AdjustSDK/AdjustSDK.h>
#import <AdjustSdk/ADJConfig.h>

@interface AdjustWrapper : NSObject <AdjustDelegate>

+ (instancetype)sharedInstance;
- (void)initAdjust:(NSDictionary *)launchOptions;
- (void)initSdk;
- (BOOL)handleOpenURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;
- (void)handleURL:(NSURL *)url;
- (NSString *)getDeepLink;
- (NSString *)getAdjustAdId;
- (NSString *)getAdjustIdfa;
- (NSString *)getAttribution;
- (void)trackEventWithEventName:(NSString *)eventName;
- (void)trackPayEventWithEventName:(NSString *)eventName currency:(NSString *)currency payValue:(double)payValue;
- (void)adjustAttributionChanged:(ADJAttribution *)attribution;
- (BOOL)adjustDeferredDeeplinkReceived:(NSURL *)deeplink;
- (void)trackAdRevenueWithRevenue:(double)revenue
                         currency:(NSString *)currency
                      networkName:(NSString *)networkName
                 adUnitIdentifier:(NSString *)adUnitIdentifier
                        placement:(NSString *)placement;
- (void)trackFacebookSharing:(int)stateCode;
- (void)trackGdprSharing:(int)stateCode;
- (void)endFirstSessionDelay;
@end

