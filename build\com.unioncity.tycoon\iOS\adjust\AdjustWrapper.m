#import "AdjustWrapper.h"
#import "Utils.h"
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <AdSupport/AdSupport.h>
#import <AdjustSdk/ADJConfig.h>
#import <AdjustSdk/Adjust.h>
#import <AdjustSdk/ADJLogger.h>
#import <AdjustSdk/ADJAttribution.h>
#import <AdjustSdk/ADJEvent.h>
#import <AdjustSdk/ADJAdRevenue.h>
#import <AdjustSdk/ADJThirdPartySharing.h>

@interface AdjustWrapper ()

// 属性存储
@property (nonatomic, strong) NSString *deepLink;
@property (nonatomic, strong) NSString *adjustIdfa;
@property (nonatomic, strong) NSString *adjustAdId;
@property (nonatomic, strong) NSString *adjustAttribution;
@property (nonatomic, strong) ADJConfig *adjustConfig; // 将 adjustConfig 作为属性以便在多个方法中访问

@end

@implementation AdjustWrapper

#pragma mark - Singleton Instance

+ (instancetype)sharedInstance {
    static AdjustWrapper *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[AdjustWrapper alloc] init];
    });
    return sharedInstance;
}

#pragma mark - Initialization

- (void)initAdjust:(NSDictionary *)launchOptions {
    NSString *appToken = @"my3qfz0tti4g"; 
    self.adjustConfig = [[ADJConfig alloc] initWithAppToken:appToken environment:ADJEnvironmentProduction];
    //[self.adjustConfig enableCostDataInAttribution];
    [self.adjustConfig setLogLevel:ADJLogLevelError];
    [self.adjustConfig enableFirstSessionDelay];

    [self.adjustConfig setDelegate:self];
}

- (void)initSdk {
    [Adjust initSdk:self.adjustConfig];

    [self fetchIDFA];

    [Adjust adidWithCompletionHandler:^(NSString * _Nullable adid) {
        self.adjustAdId = adid;
        NSLog(@"SDK.Adjust.onAdidRead : %@", self.adjustAdId);
    }];

    [Adjust attributionWithCompletionHandler:^(ADJAttribution * _Nullable attribution) {
        [self handleAttribution:attribution];
    }];
}

- (void)trackFacebookSharing:(int)stateCode {
    // 1 for USA, as per Facebook's LDU documentation.
    ADJThirdPartySharing *tps = [[ADJThirdPartySharing alloc] initWithIsEnabled:nil];
    [tps addGranularOption:@"facebook" key:@"data_processing_options_country" value:@"1"];
    [tps addGranularOption:@"facebook" key:@"data_processing_options_state" value:[NSString stringWithFormat:@"%d", stateCode]];
    [Adjust trackThirdPartySharing:tps];
}

- (void)trackGdprSharing:(int)stateCode  {
    ADJThirdPartySharing *adjustThirdPartySharing = [[ADJThirdPartySharing alloc]
                                                        initWithIsEnabled:nil];
    [adjustThirdPartySharing addGranularOption:@"google_dma" key:@"eea" value:@"1"];
    [adjustThirdPartySharing addGranularOption:@"google_dma" key:@"ad_personalization" value:[NSString stringWithFormat:@"%d", stateCode]];
    [adjustThirdPartySharing addGranularOption:@"google_dma" key:@"ad_user_data" value:[NSString stringWithFormat:@"%d", stateCode]];
    [Adjust trackThirdPartySharing:adjustThirdPartySharing];
}

- (void)endFirstSessionDelay {
    [Adjust endFirstSessionDelay];
}

- (void)fetchIDFA {
    if (@available(iOS 14.5, *)) {
        // iOS 14.5 及之后使用 App Tracking Transparency (ATT)
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        if (status == ATTrackingManagerAuthorizationStatusAuthorized) {
            [self fetchIDFAFromAdjust];
        } else {
            NSLog(@"ATT 未授权或受限，无法获取 IDFA");
            self.adjustIdfa = nil;
        }
    } else {
        // iOS 14.5 以下版本使用传统方式检查 isAdvertisingTrackingEnabled
        if ([[ASIdentifierManager sharedManager] isAdvertisingTrackingEnabled]) {
            [self fetchIDFAFromAdjust];
        } else {
            NSLog(@"isAdvertisingTrackingEnabled == NO，无法获取 IDFA");
            self.adjustIdfa = nil;
        }
    }
}

- (void)fetchIDFAFromAdjust {
    [Adjust idfaWithCompletionHandler:^(NSString * _Nullable idfa) {
        self.adjustIdfa = idfa;
        NSLog(@"SDK.Adjust.getidfa : %@", self.adjustIdfa);
    }];
}

#pragma mark - Handle Attribution

- (void)handleAttribution:(ADJAttribution *)attribution {
    if (attribution == nil) {
        return;
    }

    NSMutableDictionary *jsonDict = [NSMutableDictionary dictionary];
    if (attribution.trackerToken) {
        jsonDict[@"trackerToken"] = attribution.trackerToken;
    }
    if (attribution.trackerName) {
        jsonDict[@"trackerName"] = attribution.trackerName;
    }
    if (attribution.network) {
        jsonDict[@"network"] = attribution.network;
    }
    if (attribution.campaign) {
        jsonDict[@"campaign"] = attribution.campaign;
    }
    if (attribution.adgroup) {
        jsonDict[@"adgroup"] = attribution.adgroup;
    }
    if (attribution.creative) {
        jsonDict[@"creative"] = attribution.creative;
    }
    if (attribution.clickLabel) {
        jsonDict[@"clickLabel"] = attribution.clickLabel;
    }
    if (attribution.costCurrency) {
        jsonDict[@"costCurrency"] = attribution.costCurrency;
    }
    if (attribution.costAmount) {
        jsonDict[@"costAmount"] = attribution.costAmount;
    }

    NSString *result = [Utils dictToJson:jsonDict];
    if (result){
        self.adjustAttribution = result;
        // 修正：将 UnitySendMessage 调度到主线程执行
        dispatch_async(dispatch_get_main_queue(), ^{
            UnitySendMessage("SdkWrapper", "Adjust_Attribution_Changed", [self.adjustAttribution UTF8String]);
        });
    }
}

#pragma mark - Handle Open URL

- (BOOL)handleOpenURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    if (url) {
        self.deepLink = [url absoluteString];
        NSLog(@"SDK.Adjust handleOpenURL Deep link URL: %@", self.deepLink);

        return YES;
    }
    return NO;
}

- (void)handleURL:(NSURL *)url {
    if (url) {
        self.deepLink = [url absoluteString];
        NSLog(@"SDK.Adjust handleURL Deep link URL: %@", self.deepLink);
    }
}

#pragma mark - Getters

- (NSString *)getDeepLink {
    return self.deepLink;
}

- (NSString *)getAdjustAdId {
    return self.adjustAdId;
}

- (NSString *)getAdjustIdfa {
    return self.adjustIdfa;
}

- (NSString *)getAttribution {
    return self.adjustAttribution;
}

#pragma mark - Track Event

- (void)trackEventWithEventName:(NSString *)eventName {
    if (eventName) {
        ADJEvent *event = [[ADJEvent alloc] initWithEventToken:eventName];
        [Adjust trackEvent:event];
        NSLog(@"SDK.Adjust Tracked event: %@", eventName);
    }
}

- (void)trackPayEventWithEventName:(NSString *)eventName currency:(NSString *)currency payValue:(double)payValue {
    if (eventName) {
        ADJEvent *payEvent = [[ADJEvent alloc] initWithEventToken:eventName];
        [payEvent setRevenue:payValue currency:currency];
        [Adjust trackEvent:payEvent];
        NSLog(@"SDK.Adjust Tracked pay event: %@ | currency: %@ | amount: %f", eventName, currency, payValue);
    }
}
- (void)trackAdRevenueWithRevenue:(double)revenue
                         currency:(NSString *)currency
                      networkName:(NSString *)networkName
                 adUnitIdentifier:(NSString *)adUnitIdentifier
                        placement:(NSString *)placement {
    ADJAdRevenue *adRevenue = [[ADJAdRevenue alloc] initWithSource:@"applovin_max_sdk"];
    [adRevenue setRevenue:revenue currency:currency];
    [adRevenue setAdRevenueNetwork:networkName];
    [adRevenue setAdRevenueUnit:adUnitIdentifier];
    [adRevenue setAdRevenuePlacement:placement];
    [Adjust trackAdRevenue:adRevenue];
    NSLog(@"SDK.Adjust Tracked Ad Revenue");
}

#pragma mark - ADJConfigDelegate

- (void)adjustAttributionChanged:(ADJAttribution *)attribution {
   // 此方法在子线程被调用
   [self handleAttribution:attribution];
}

- (BOOL)adjustDeferredDeeplinkReceived:(NSURL *)deeplink {
    if (deeplink) {
        NSLog(@"SDK.Adjust.setDeferredDeeplinkResponseListener URL: %@", deeplink);
        self.deepLink = [deeplink absoluteString];
        return YES;
    }
    return NO;
}

@end
