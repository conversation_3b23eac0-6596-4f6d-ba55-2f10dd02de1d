#import "AppReviewWrapper.h"
#import <StoreKit/StoreKit.h>
#import <UIKit/UIKit.h> // For UIWindowScene

@implementation AppReviewWrapper

static AppReviewWrapper *sharedInstance = nil;

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

// Private init to enforce singleton
- (instancetype)init {
    if (self = [super init]) {
        // Any one-time initialization for the instance can go here
    }
    return self;
}

- (void)requestReview {
    if (@available(iOS 10.3, *)) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (@available(iOS 14.0, *)) {
                UIWindowScene *activeScene = nil;
                if ([[UIApplication sharedApplication] respondsToSelector:@selector(connectedScenes)]) {
                    for (UIWindowScene *scene in [UIApplication sharedApplication].connectedScenes) {
                        if (scene.activationState == UISceneActivationStateForegroundActive) {
                            activeScene = scene;
                            break;
                        }
                    }
                }
                
                if (activeScene) {
                    [SKStoreReviewController requestReviewInScene:activeScene];
                    NSLog(@"[AppReviewWrapper] SKStoreReviewController.requestReviewInScene called via instance.");
                } else {
                    [SKStoreReviewController requestReview];
                    NSLog(@"[AppReviewWrapper] SKStoreReviewController.requestReview called (no active scene or pre-iOS 14) via instance.");
                }
            } else {
                // Fallback for iOS 10.3 to 13.x
                [SKStoreReviewController requestReview];
                NSLog(@"[AppReviewWrapper] SKStoreReviewController.requestReview called (iOS 10.3-13.x) via instance.");
            }
            UnitySendMessage("SdkWrapper", "Review_End", "true");
        });
    } else {
        NSLog(@"[AppReviewWrapper] SKStoreReviewController is not available on this iOS version (requires iOS 10.3+).");
        UnitySendMessage("SdkWrapper", "Review_End", "false");
    }
}

@end