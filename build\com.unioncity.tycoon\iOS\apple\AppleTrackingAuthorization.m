#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import "AppleTrackingAuthorization.h"

@implementation AppleTrackingAuthorization

// 请求追踪权限
+ (void)requestTrackingAuthorization{
    if (@available(iOS 14, *)) {
        // 获取当前的授权状态
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        NSLog(@"[SDK.TrackingAuthorization]: %d", (int)status);

        if (status == ATTrackingManagerAuthorizationStatusNotDetermined) {
            // 如果授权状态为未决定，才请求用户授权
            [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus newStatus) {
                // 修正：将 UnitySendMessage 调度到主线程执行
                dispatch_async(dispatch_get_main_queue(), ^{
                    NSLog(@"[SDK.TrackingAuthorization]: %d", (int)newStatus);
                    NSString *statusString = [self stringForTrackingAuthorizationStatus:newStatus];
                    UnitySendMessage("SdkWrapper", "Apple_Tracking_Authorization", [statusString UTF8String]);
                });
            }];
        } else {
            // 授权状态已经确定，直接返回当前状态
            NSString *statusString = [self stringForTrackingAuthorizationStatus:status];
            UnitySendMessage("SdkWrapper", "Apple_Tracking_Authorization", [statusString UTF8String]);
        }
    } else {
        // iOS 14 以下版本，默认授权
        NSLog(@"[SDK.TrackingAuthorization]: available iOS14");
        NSString *statusString =  @"Authorized";
        UnitySendMessage("SdkWrapper", "Apple_Tracking_Authorization", [statusString UTF8String]);
    }
}

+ (NSString *)stringForTrackingAuthorizationStatus:(ATTrackingManagerAuthorizationStatus)status {
    switch (status) {
        case ATTrackingManagerAuthorizationStatusNotDetermined:
            return @"NotDetermined";
        case ATTrackingManagerAuthorizationStatusRestricted:
            return @"Restricted";
        case ATTrackingManagerAuthorizationStatusDenied:
            return @"Denied";
        case ATTrackingManagerAuthorizationStatusAuthorized:
            return @"Authorized";
        default:
            return @"Authorized";
    }
}

@end