// FacebookWrapper.h
#import <Foundation/Foundation.h>
#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import <FBSDKLoginKit/FBSDKLoginKit.h>
#import <FBSDKShareKit/FBSDKShareKit.h>

@interface FacebookWrapper : NSObject<FBSDKSharingDelegate>

// 单例方法
+ (instancetype)sharedInstance;
- (void)initFacebook:(NSDictionary*)options;
- (void)initSdk;
- (void)FacebookLogin;
- (void)FacebookLogout;
- (void)FacebookShare:(NSString *)data;
- (void)FacebookTrack:(NSString *)key property:(NSString *)property;
- (void)FacebookSetDataProcessingOptions:(int)stateCode;
- (void)FacebookLoginWithPermissions:(NSString *)permissions;
@end
