// FacebookWrapper.m
#import "FacebookWrapper.h"
#import <FBAudienceNetwork/FBAudienceNetwork.h>
#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import <FBSDKLoginKit/FBSDKLoginKit.h>
#import <FBSDKShareKit/FBSDKShareKit.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import "Utils.h"

// 私有属性扩展
@interface FacebookWrapper ()
@property (nonatomic, strong) NSDictionary *launchOptions;
@end

@implementation FacebookWrapper

// 单例实现
+ (instancetype)sharedInstance {
    static FacebookWrapper *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

// Facebook SDK初始化
- (void)initFacebook:(NSDictionary*)options{
    // 保存启动选项的副本，供后续使用
    self.launchOptions = [options copy];
    
    // 设置Facebook App ID
    [FBSDKSettings.sharedSettings setAppID:@"1706982233576600"];
    NSLog(@"[SDK.Facebook] App ID set to: %@", FBSDKSettings.sharedSettings.appID);
    
}

- (void)FacebookSetDataProcessingOptions:(int)stateCode {
    NSArray<NSString *> *options = @[@"LDU"];
    int country = 1; // 1 for USA, as per Facebook's LDU documentation.

    // 为 Audience Network SDK 设置
    [FBAdSettings setDataProcessingOptions:options country:country state:stateCode];
    
    // 为 Core SDK 设置 (保持同步)
    [FBSDKSettings.sharedSettings setDataProcessingOptions:options country:country state:stateCode];
    NSLog(@"[SDK.Facebook] Set LDU for both SDKs with country: %d, state: %d", country, stateCode);
}

- (void)initSdk {
    // 确保App ID已设置
    if (!FBSDKSettings.sharedSettings.appID || [FBSDKSettings.sharedSettings.appID length] == 0) {
        [FBSDKSettings.sharedSettings setAppID:@"1706982233576600"];
        NSLog(@"[SDK.Facebook] App ID was not set, setting to: %@", FBSDKSettings.sharedSettings.appID);
    }
    
    // 检查 App Tracking Transparency (ATT) 授权状态
    if (@available(iOS 14.5, *)) {
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        BOOL isAuthorized = (status == ATTrackingManagerAuthorizationStatusAuthorized);

        // 核心SDK的设置总是需要的
        FBSDKSettings.sharedSettings.isAdvertiserTrackingEnabled = isAuthorized;

        // Audience Network SDK的设置仅在iOS 17以下版本需要
        if (@available(iOS 17.0, *)) {
            // 在iOS 17+上，Audience Network SDK会自动检查ATT状态，此方法已弃用。
            NSLog(@"[SDK.Facebook] On iOS 17+, Audience Network SDK handles advertiserTrackingEnabled automatically.");
        } else {
            [FBAdSettings setAdvertiserTrackingEnabled:isAuthorized];
        }
        
        if (isAuthorized) {
            NSLog(@"[SDK.Facebook] ATT Authorized: Advertiser tracking is enabled.");
        } else {
            NSLog(@"[SDK.Facebook] ATT Not Authorized: Advertiser tracking is disabled.");
        }
    } else {
        // iOS 14.5 以下版本，默认启用广告追踪
        [FBAdSettings setAdvertiserTrackingEnabled:YES];
        FBSDKSettings.sharedSettings.isAdvertiserTrackingEnabled = YES;
        NSLog(@"[SDK.Facebook] ATT not available. Advertiser tracking is enabled by default.");
    }

    // 启动事件自动上报
    FBSDKSettings.sharedSettings.isAutoLogAppEventsEnabled = YES;
    
    // 初始化Facebook SDK
    [[FBSDKApplicationDelegate sharedInstance] application:[UIApplication sharedApplication]
                             didFinishLaunchingWithOptions:self.launchOptions];
    NSLog(@"[SDK.Facebook] Core SDK initialized successfully.");
    
    // 初始化 Audience Network
    [FBAudienceNetworkAds initializeWithSettings:nil completionHandler:nil];
    NSLog(@"[SDK.Facebook] Audience Network initialized successfully.");
}

- (void)FacebookLoginWithPermissions:(NSString *)permissions {
        dispatch_async(dispatch_get_main_queue(), ^{
        FBSDKLoginManager *loginManager = [[FBSDKLoginManager alloc] init];
        
        // 解析 permissions 字符串为数组
        NSArray<NSString *> *permissionsArray;
        if (permissions && permissions.length > 0) {
            permissionsArray = [permissions componentsSeparatedByString:@","];
            // 去除每个权限字符串的前后空格
            NSMutableArray<NSString *> *trimmedPermissions = [NSMutableArray array];
            for (NSString *permission in permissionsArray) {
                [trimmedPermissions addObject:[permission stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]]];
            }
            permissionsArray = [trimmedPermissions copy];
        } else {
            // 如果没有传入权限或权限为空，使用默认权限
            permissionsArray = @[@"public_profile", @"user_friends", @"email"];
        }
        
        // 检查是否可以使用 FBSDKLoginConfiguration (iOS 13+)
        if (@available(iOS 13.0, *)) {
            
            // 1. 根据ATT状态决定追踪模式
            BOOL isTrackingAuthorized = NO;
            if (@available(iOS 14.5, *)) {
                isTrackingAuthorized = ([ATTrackingManager trackingAuthorizationStatus] == ATTrackingManagerAuthorizationStatusAuthorized);
            } else {
                // 对于低于iOS 14.5的系统，可以认为追踪是默认启用的
                isTrackingAuthorized = YES;
            }
            
            FBSDKLoginTracking tracking = isTrackingAuthorized ? FBSDKLoginTrackingEnabled : FBSDKLoginTrackingLimited;
            if (isTrackingAuthorized) {
                 NSLog(@"[SDK.Facebook] Login with tracking ENABLED.");
            } else {
                 NSLog(@"[SDK.Facebook] Login with tracking LIMITED.");
            }

            // 2. 使用解析后的权限数组创建配置
            FBSDKLoginConfiguration *config = [[FBSDKLoginConfiguration alloc] 
                initWithPermissions:permissionsArray
                           tracking:tracking];
            
            // 3. 使用配置进行登录
            UIViewController *view = (UIViewController*)[UIApplication sharedApplication].keyWindow.rootViewController;
            [loginManager logInFromViewController:view
                                    configuration:config
                                       completion:^(FBSDKLoginManagerLoginResult *result, NSError *error) {
                [self handleLoginResult:result error:error];
            }];

        } else {
            // 为旧版iOS保留传统登录方式
            UIViewController *view = (UIViewController*)[UIApplication sharedApplication].keyWindow.rootViewController;
            [loginManager logInWithPermissions:permissionsArray
                            fromViewController:view
                                       handler:^(FBSDKLoginManagerLoginResult *result, NSError *error) {
                [self handleLoginResult:result error:error];
            }];
        }
    });
}

// Facebook登录
- (void)FacebookLogin {
    dispatch_async(dispatch_get_main_queue(), ^{
        FBSDKLoginManager *loginManager = [[FBSDKLoginManager alloc] init];
        
        // 检查是否可以使用 FBSDKLoginConfiguration (iOS 13+)
        if (@available(iOS 13.0, *)) {
            
            // 1. 根据ATT状态决定追踪模式
            BOOL isTrackingAuthorized = NO;
            if (@available(iOS 14.5, *)) {
                isTrackingAuthorized = ([ATTrackingManager trackingAuthorizationStatus] == ATTrackingManagerAuthorizationStatusAuthorized);
            } else {
                // 对于低于iOS 14.5的系统，可以认为追踪是默认启用的
                isTrackingAuthorized = YES;
            }
            
            FBSDKLoginTracking tracking = isTrackingAuthorized ? FBSDKLoginTrackingEnabled : FBSDKLoginTrackingLimited;
            if (isTrackingAuthorized) {
                 NSLog(@"[SDK.Facebook] Login with tracking ENABLED.");
            } else {
                 NSLog(@"[SDK.Facebook] Login with tracking LIMITED.");
            }

            // 2. 使用正确的枚举值创建配置
            FBSDKLoginConfiguration *config = [[FBSDKLoginConfiguration alloc] 
                initWithPermissions:@[@"public_profile", @"user_friends", @"email"]
                           tracking:tracking];
            
            // 3. 使用配置进行登录
            UIViewController *view = (UIViewController*)[UIApplication sharedApplication].keyWindow.rootViewController;
            [loginManager logInFromViewController:view
                                    configuration:config
                                       completion:^(FBSDKLoginManagerLoginResult *result, NSError *error) {
                [self handleLoginResult:result error:error];
            }];

        } else {
            // 为旧版iOS保留传统登录方式
            UIViewController *view = (UIViewController*)[UIApplication sharedApplication].keyWindow.rootViewController;
            [loginManager logInWithPermissions:@[@"public_profile", @"user_friends", @"email"]
                            fromViewController:view
                                       handler:^(FBSDKLoginManagerLoginResult *result, NSError *error) {
                [self handleLoginResult:result error:error];
            }];
        }
    });
}

// 提取登录结果处理逻辑
- (void)handleLoginResult:(FBSDKLoginManagerLoginResult *)result error:(NSError *)error {
    if (error) {
        NSLog(@"[SDK.Facebook] Login failed with error: %@", error);

        NSString *errorString = [NSString stringWithFormat:@"Error: %@ (Code: %ld)", error.localizedDescription, (long)error.code];
        UnitySendMessage("SdkWrapper", "Facebook_Login_Fail", [errorString UTF8String]);
    } else if (result.isCancelled) {
        NSLog(@"[SDK.Facebook] Login was cancelled.");
        NSString *statusString = @"cacel";
        UnitySendMessage("SdkWrapper", "Facebook_Login_Fail", [statusString UTF8String]);
    } else {
        NSLog(@"[SDK.Facebook] Login succeeded.");

        FBSDKAccessToken *accessToken = [FBSDKAccessToken currentAccessToken];

        NSMutableDictionary *dict = [NSMutableDictionary dictionary];

        if(result.authenticationToken){
            NSString *authToken = result.authenticationToken.tokenString;
            if (authToken)
            {
                [dict setObject:authToken forKey:@"AuthToken"];
                NSLog(@"[SDK.Facebook] Limited Login, AuthToken is available.");
            }
        }

        if (accessToken.tokenString)
        {
            [dict setObject:accessToken.tokenString forKey:@"Token"];
        }

        if (accessToken.userID)
        {
            [dict setObject:accessToken.userID forKey:@"UserId"];
        }

        if (accessToken.appID)
        {
            [dict setObject:accessToken.appID forKey:@"ApplicationId"];
        }

        [dict setObject:[NSString stringWithFormat:@"%ld", (long)[accessToken.expirationDate timeIntervalSince1970]] forKey:@"Expires"];
        [dict setObject:[NSString stringWithFormat:@"%ld", (long)[accessToken.refreshDate timeIntervalSince1970]] forKey:@"LastRefresh"];

        NSDictionary *finalDict = [dict copy];
        NSString* tokenJsonStr = [Utils dictToJson:finalDict];
        UnitySendMessage("SdkWrapper", "Facebook_Login_Success", [tokenJsonStr UTF8String]);
    }
}

// Facebook登出
- (void)FacebookLogout {
    dispatch_async(dispatch_get_main_queue(), ^{
        FBSDKLoginManager *loginManager = [[FBSDKLoginManager alloc] init];
        [loginManager logOut];
        NSLog(@"[SDK.Facebook] Logged Out.");
    });
}

// Facebook分享
- (void)FacebookShare:(NSString *)data {
    dispatch_async(dispatch_get_main_queue(), ^{

        NSDictionary *shareData = [Utils dictFromJson:data];
        if (!shareData)
        {
            NSLog(@"[SDK.Facebook] Error parsing JSON");
            UnitySendMessage("SdkWrapper", "Facebook_Share_Fail", [@"Invalid JSON format" UTF8String]);
            return;
        }
        
        NSString *contentURL = shareData[@"link"];
        NSString *description = shareData[@"desc"];
        NSString *title = shareData[@"title"];
        
        FBSDKShareLinkContent *content = [[FBSDKShareLinkContent alloc] init];
        content.contentURL = [NSURL URLWithString:contentURL];
        content.quote = description;

        [content setHashtag:[[FBSDKHashtag alloc] initWithString:title]];

        UIViewController *view = (UIViewController*)[UIApplication sharedApplication].keyWindow.rootViewController;
        FBSDKShareDialog *dialog = [[FBSDKShareDialog alloc] initWithViewController:view content:content delegate:self];
        dialog.mode = FBSDKShareDialogModeNative;
        
        if ([dialog canShow]) {
            [dialog show];
        } else {
            NSLog(@"[SDK.Facebook] Selected mode is not supported on this device.");
            NSString *statusString = @"error";
            UnitySendMessage("SdkWrapper", "Facebook_Share_Fail", [statusString UTF8String]);
        }
    });
}

- (void)FacebookTrack:(NSString *)key property:(NSString *)property {
    NSDictionary *properties = [Utils dictFromJson:property];
    if (properties) {
        // 如果字典里包含 "valueToSum"，则尝试取出并转为 double
        if ([properties.allKeys containsObject:@"valueToSum"]) {
            id valueToSumObj = properties[@"valueToSum"];
            
            // 判断是否能转换为 double
            double valueToSum = 0.0;
            if ([valueToSumObj respondsToSelector:@selector(doubleValue)]) {
                valueToSum = [valueToSumObj doubleValue];
            } else {
                // 如果无法解析为数字，可按需求处理，比如设置一个默认值，或者直接跳过
                NSLog(@"[SDK.Facebook] valueToSum is invalid, setting default to 0.0");
            }
            
            // 调用带有 valueToSum 的方法
            [FBSDKAppEvents.shared logEvent:key
                                 valueToSum:valueToSum
                                 parameters:properties];
        } else {
            // 如果没有 "valueToSum"，直接调用不带 valueToSum 的方法
            [FBSDKAppEvents.shared logEvent:key parameters:properties];
        }
    } else {
        NSLog(@"[SDK.Facebook] Track property nil");
    }
}

// 实现 FBSDKSharingDelegate 方法
#pragma mark - FBSDKSharingDelegate

// 分享成功
- (void)sharer:(id<FBSDKSharing>)sharer didCompleteWithResults:(NSDictionary<NSString *, id> *)results {
    NSLog(@"[SDK.Facebook] Share succeeded with results: %@", results);
    NSString *statusString = @"success";
    UnitySendMessage("SdkWrapper", "Facebook_Share_Success", [statusString UTF8String]);
}

// 分享取消
- (void)sharerDidCancel:(id<FBSDKSharing>)sharer {
    NSLog(@"[SDK.Facebook] Share was cancelled");
    NSString *statusString = @"cancel";
    UnitySendMessage("SdkWrapper", "Facebook_Share_Fail", [statusString UTF8String]);
}

// 分享失败
- (void)sharer:(id<FBSDKSharing>)sharer didFailWithError:(NSError *)error {
    NSLog(@"[SDK.Facebook] Share failed with error: %@", error);
    NSString *statusString = @"error";
    UnitySendMessage("SdkWrapper", "Facebook_Share_Fail", [statusString UTF8String]);
}
@end
