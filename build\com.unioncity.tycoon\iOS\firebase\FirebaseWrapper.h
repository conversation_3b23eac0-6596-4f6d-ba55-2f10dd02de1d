#import <Foundation/Foundation.h>
#import <FirebaseMessaging.h>
#import <UserNotifications/UserNotifications.h>

NS_ASSUME_NONNULL_BEGIN

@interface FirebaseWrapper : NSObject <FIRMessagingDelegate, UNUserNotificationCenterDelegate>

+ (instancetype)sharedInstance;
- (void)initFirebase;
- (void)registerForRemoteNotifications;
- (void)getFCMToken;
- (void)logEvent:(NSString *)key property:(NSString *)property;
- (void)setQuietTime:(int)startHour startMinute:(int)startMinute endHour:(int)endHour endMinute:(int)endMinute;
- (void)setAnalyticsCollectionEnabled;

- (void)userNotificationCenter:(UNUserNotificationCenter *)center
       willPresentNotification:(UNNotification *)notification
         withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler;

- (void)userNotificationCenter:(UNUserNotificationCenter *)center
            didReceiveNotificationResponse:(UNNotificationResponse *)response
                      withCompletionHandler:(void (^)(void))completionHandler;
- (void)application:(UIApplication *)application
        didFirebaseReceiveRemoteNotification:(NSDictionary *)userInfo
            fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler;
- (void)displayNotificationWithTitle:(NSString *)title body:(NSString *)body imageURL:(NSString *)imageURL;
// 处理远程通知注册成功
- (void)application:(UIApplication *)application didFirebaseRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken;

// 处理远程通知注册失败
- (void)application:(UIApplication *)application didFirebaseFailToRegisterForRemoteNotificationsWithError:(NSError *)error;
@end

NS_ASSUME_NONNULL_END
