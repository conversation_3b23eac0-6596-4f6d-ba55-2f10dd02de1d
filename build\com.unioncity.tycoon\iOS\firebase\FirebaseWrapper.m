#import "FirebaseWrapper.h"
#import "Utils.h"
#import <FirebaseCore.h>
#import <FirebaseMessaging.h>
#import <FirebaseAnalytics/FirebaseAnalytics.h>
#import <UserNotifications/UserNotifications.h>

@implementation FirebaseWrapper

+ (instancetype)sharedInstance {
    static FirebaseWrapper *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (void)initFirebase {
    @autoreleasepool {
        // 初始化 Firebase
            [FIRApp configure];
            NSLog(@"[SDK.Firebase] Firebase initialized");
            
            // 设置代理
            [FIRMessaging messaging].delegate = self;
            UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
            center.delegate = self;  
    }
}

// 处理远程通知注册成功
- (void)application:(UIApplication *)application didFirebaseRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    NSLog(@"[SDK.Firebase] Successfully registered for remote notifications with token: %@", deviceToken);
    // 将设备令牌传递给 Firebase
    [[FIRMessaging messaging] setAPNSToken:deviceToken type:FIRMessagingAPNSTokenTypeUnknown];
}

// 处理远程通知注册失败
- (void)application:(UIApplication *)application didFirebaseFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
    NSLog(@"[SDK.Firebase] Failed to register for remote notifications: %@", error);
}

// 处理收到远程通知
- (void)application:(UIApplication *)application didFirebaseReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler {
    NSLog(@"[SDK.Firebase] Received remote notification: %@", userInfo);
    
        NSString *title = userInfo[@"title"];
        NSString *body = userInfo[@"body"];
        NSString *imageURL = userInfo[@"image"];
        NSString *args = userInfo[@"args"];
        NSString *notificationBody=body;
        if (args != nil && [args length] > 0){
            NSArray *parameters = [args componentsSeparatedByString:@","];
            notificationBody = [self formatTemplate:body withParameters:parameters];
        }
        
        [self displayNotificationWithTitle:title body:notificationBody imageURL:imageURL];
    
    completionHandler(UIBackgroundFetchResultNewData);
}

// 注册远程通知并请求用户权限
- (void)registerForRemoteNotifications {
    @autoreleasepool {
        // 请求通知权限
        UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
        [center requestAuthorizationWithOptions:(UNAuthorizationOptionAlert |
                                                 UNAuthorizationOptionBadge |
                                                 UNAuthorizationOptionSound)
                           completionHandler:^(BOOL granted, NSError * _Nullable error) {
            if (error) {
                NSLog(@"[SDK.Firebase] Error requesting notification permissions: %@", error);
                return;
            }
            if (granted) {
                // 注册 APNs
                // 将 UI API 调用派发到主线程
                dispatch_async(dispatch_get_main_queue(), ^{
                    [[UIApplication sharedApplication] registerForRemoteNotifications];
                    NSLog(@"[SDK.Firebase] Notification permissions granted.");
                });
            } else {
                NSLog(@"[SDK.Firebase] Notification permissions denied.");
            }
        }];
    }
}

// 获取 FCM Token
- (void)getFCMToken {
    @autoreleasepool {
        // 获取 Firebase FCM Token
        [[FIRMessaging messaging] tokenWithCompletion:^(NSString * _Nullable token, NSError * _Nullable error) {
            if (error != nil) {
                NSLog(@"Error fetching FCM token: %@", error);
                return;
            }
            NSLog(@"[SDK.Firebase] FCM token: %@", token);
            
            // 将 token 传递给 Unity C# 层
            if (token != nil) {
                UnitySendMessage("SdkWrapper", "Firebase_Token_Success", [token UTF8String]);
            }
        }];
    }
}

- (void)setAnalyticsCollectionEnabled{
    @autoreleasepool {
        [FIRAnalytics setAnalyticsCollectionEnabled:YES];
        NSLog(@"[SDK.Firebase] Analytics collection enabled");
    }
}

// 记录 Firebase 事件
- (void)logEvent:(NSString *)key property:(NSString *)property {
    @autoreleasepool {
        NSDictionary *jsonDict = [Utils dictFromJson:property];
        
        if (jsonDict == nil) {
            NSLog(@"[SDK.Firebase] Error parsing property");
            return;
        }

        // 创建 Firebase event parameters 字典
        NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
        
        // 遍历 JSON 字典，填充 Firebase 参数
        for (NSString *key in jsonDict) {
            id value = jsonDict[key];
            if ([value isKindOfClass:[NSString class]]) {
                parameters[key] = value;
            } else if ([value isKindOfClass:[NSNumber class]]) {
                parameters[key] = value;
            }
        }

        // 使用 Firebase Analytics 记录事件
        [FIRAnalytics logEventWithName:key parameters:parameters];
        
        NSLog(@"[SDK.Firebase] log Firebase event: %@ with parameters: %@", key, parameters);
    }
}

// 处理 FCM Token 刷新
- (void)messaging:(FIRMessaging *)messaging didReceiveRegistrationToken:(NSString *)fcmToken {
    @autoreleasepool {
        // 处理令牌刷新
        NSLog(@"[SDK.Firebase] FCM registration token: %@", fcmToken);
        
        // 通过通知中心通知令牌更新
        NSDictionary *dataDict = @{@"token": fcmToken};
        [[NSNotificationCenter defaultCenter] postNotificationName:@"FCMToken" object:nil userInfo:dataDict];
        
        // 将令牌传递给 Unity C# 层
        UnitySendMessage("SdkWrapper", "Firebase_Token_Success", [fcmToken UTF8String]);
    }
}

// UNUserNotificationCenterDelegate 方法 - 处理通知点击事件
- (void)userNotificationCenter:(UNUserNotificationCenter *)center
       willPresentNotification:(UNNotification *)notification
         withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler {
    NSLog(@"[SDK.Firebase] Will present notification in foreground");
    
    // 确保在前台显示通知
    if (@available(iOS 14.0, *)) {
        completionHandler(UNNotificationPresentationOptionBanner |
                        UNNotificationPresentationOptionList |
                        UNNotificationPresentationOptionSound |
                        UNNotificationPresentationOptionBadge);
    } else {
        completionHandler(UNNotificationPresentationOptionAlert |
                        UNNotificationPresentationOptionSound |
                        UNNotificationPresentationOptionBadge);
    }
}

// 处理点击通知后打开应用时的行为
- (void)userNotificationCenter:(UNUserNotificationCenter *)center
            didReceiveNotificationResponse:(UNNotificationResponse *)response
                      withCompletionHandler:(void (^)(void))completionHandler {
    NSLog(@"[SDK.Firebase] Notification clicked: %@", response.notification.request.content.userInfo);
    
    // 这里可以根据通知内容执行不同的逻辑
    completionHandler();
}
- (void)setQuietTime:(int)startHour startMinute:(int)startMinute endHour:(int)endHour endMinute:(int)endMinute {
    @autoreleasepool {
        // 设置 Firebase 的静默时间
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        [defaults setInteger:startHour forKey:@"QuietTimeStartHour"];
        [defaults setInteger:startMinute forKey:@"QuietTimeStartMinute"];
        [defaults setInteger:endHour forKey:@"QuietTimeEndHour"];
        [defaults setInteger:endMinute forKey:@"QuietTimeEndMinute"];
        [defaults synchronize];
    }
}
- (NSString *)formatTemplate:(NSString *)template withParameters:(NSArray *)parameters {
    NSMutableString *formattedString = [template mutableCopy];
    for (NSInteger i = 0; i < parameters.count; i++) {
        NSString *placeholder = [NSString stringWithFormat:@"{%ld}", (long)i];
        [formattedString replaceOccurrencesOfString:placeholder
                                         withString:parameters[i]
                                            options:0
                                              range:NSMakeRange(0, formattedString.length)];
    }
    return [formattedString copy];
}
//通知展示
- (void)displayNotificationWithTitle:(NSString *)title body:(NSString *)body imageURL:(NSString *)imageURL {
    // 确保在主线程执行
    dispatch_async(dispatch_get_main_queue(), ^{
        UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
        
        if(![self isCurrentTimeInQuietHours]){
            NSLog(@"[SDK.Firebase] Current time is in quiet hours, skipping notification display");
            return;
        }
        // 1. 先检查通知权限
        [center getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings * _Nonnull settings) {
            NSLog(@"[SDK.Firebase] Checking notification settings before display");
            NSLog(@"[SDK.Firebase] Authorization status: %ld", (long)settings.authorizationStatus);
            NSLog(@"[SDK.Firebase] Alert setting: %ld", (long)settings.alertSetting);
            
            if (settings.authorizationStatus == UNAuthorizationStatusAuthorized) {
                // 2. 创建通知内容
                UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
                content.title = title ? title : @"";
                content.body = body ? body : @"";
                content.sound = [UNNotificationSound defaultSound];
                      // 下载并附加图片（如果有）
               if (imageURL) {
                  NSURL *url = [NSURL URLWithString:imageURL];
                  NSData *imageData = [NSData dataWithContentsOfURL:url];
                 if (imageData) {
                    NSString *identifier = [[NSUUID UUID] UUIDString];
                    NSString *filePath = [NSTemporaryDirectory() stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.png", identifier]];
                    [imageData writeToFile:filePath atomically:YES];
                    UNNotificationAttachment *attachment = [UNNotificationAttachment attachmentWithIdentifier:identifier URL:[NSURL fileURLWithPath:filePath] options:nil error:nil];
                    if (attachment) {
                    content.attachments = @[attachment];
                   }}
               }
                // 3. 设置通知优先级
                if (@available(iOS 15.0, *)) {
                    content.interruptionLevel = UNNotificationInterruptionLevelTimeSensitive;
                }
                
                // 4. 添加唯一标识
                NSString *identifier = [[NSUUID UUID] UUIDString];
                content.userInfo = @{
                    @"notification_id": identifier,
                    @"timestamp": @([[NSDate date] timeIntervalSince1970])
                };
                
                // 5. 创建触发器（立即触发）
                UNNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger 
                    triggerWithTimeInterval:0.1 
                    repeats:NO];
                
                // 6. 创建请求
                UNNotificationRequest *request = [UNNotificationRequest 
                    requestWithIdentifier:identifier 
                    content:content 
                    trigger:trigger];
                
                NSLog(@"[SDK.Firebase] Adding notification request with ID: %@", identifier);
                
                // 7. 添加通知请求
                [center addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
                    if (error) {
                        NSLog(@"[SDK.Firebase] Failed to add notification: %@", error);
                        // 如果主要方式失败，尝试直接显示通知
                        [self showImmediateNotification:title body:body];
                    } else {
                        NSLog(@"[SDK.Firebase] Notification request added successfully");
                    }
                }];
            } else {
                NSLog(@"[SDK.Firebase] Notifications not authorized, requesting permission");
            }
        }];
    });
}

// 添加新方法用于直接显示通知
- (void)showImmediateNotification:(NSString *)title body:(NSString *)body {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (@available(iOS 10.0, *)) {
            UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
            content.title = title ? title : @"";
            content.body = body ? body : @"";
            content.sound = [UNNotificationSound defaultSound];
            
            // 使用最高优先级
            if (@available(iOS 15.0, *)) {
                content.interruptionLevel = UNNotificationInterruptionLevelTimeSensitive;
            }
            
            // 立即触发
            UNNotificationRequest *request = [UNNotificationRequest 
                requestWithIdentifier:[[NSUUID UUID] UUIDString]
                content:content
                trigger:nil];  // nil trigger means immediate delivery
            
            [[UNUserNotificationCenter currentNotificationCenter] 
                addNotificationRequest:request 
                withCompletionHandler:^(NSError * _Nullable error) {
                    if (error) {
                        NSLog(@"[SDK.Firebase] Immediate notification failed: %@", error);
                    } else {
                        NSLog(@"[SDK.Firebase] Immediate notification scheduled successfully");
                    }
            }];
        }
    });
}

- (BOOL)isCurrentTimeInQuietHours {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    // 检查是否设置了静默时间
    if (![defaults objectForKey:@"QuietTimeStartHour"]) {
        return NO;  // 如果没有设置静默时间，返回false
    }
    
    NSInteger startHour = [defaults integerForKey:@"QuietTimeStartHour"];
    NSInteger startMinute = [defaults integerForKey:@"QuietTimeStartMinute"];
    NSInteger endHour = [defaults integerForKey:@"QuietTimeEndHour"];
    NSInteger endMinute = [defaults integerForKey:@"QuietTimeEndMinute"];
    
    // 获取当前时间的分钟数
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDate *now = [NSDate date];
    NSDateComponents *components = [calendar components:(NSCalendarUnitHour | NSCalendarUnitMinute) fromDate:now];
    NSInteger currentTotalMinutes = components.hour * 60 + components.minute;
    
    // 计算开始和结束时间的总分钟数
    NSInteger startTotalMinutes = startHour * 60 + startMinute;
    NSInteger endTotalMinutes = endHour * 60 + endMinute;
    
    // 处理跨天的情况
    if (endTotalMinutes <= startTotalMinutes) {
        // 跨天情况：如果当前时间小于结束时间或大于开始时间，则在静默时间内
        return (currentTotalMinutes <= endTotalMinutes || 
                currentTotalMinutes >= startTotalMinutes);
    } else {
        // 同一天的情况：如果当前时间在开始和结束时间之间，则在静默时间内
        return (currentTotalMinutes >= startTotalMinutes && 
                currentTotalMinutes <= endTotalMinutes);
    }
}
@end
