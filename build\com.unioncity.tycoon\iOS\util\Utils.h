#import <Foundation/Foundation.h>

@interface Utils : NSObject

+(NSString *)dictToJson:(id)dict;

+(NSDictionary*) dictFromJson:(NSString*)str;

+(NSString *)randomNonce:(NSInteger)length;

+(NSString *)stringBySha256HashingString:(NSString *)input;

+(NSString *)GetSystemLanguage;

+(void) Vibrator;

+(void) CopyToClipboard:(NSString*)text;

+(void) OpenNotificationSettings;

+(void) CheckNotificationEnabled;

+(void) InviteSystem:(NSString*)data;

+(long) GetAvailableSpace:(NSString*)path;

+(long) GetTotalSpace:(NSString*)path;

+(long) GetUsedSpace:(NSString*)path;

+(void) OpenFansUrl:(NSString*)data;

+(void) OpenUrl:(NSString*)url;
@end
