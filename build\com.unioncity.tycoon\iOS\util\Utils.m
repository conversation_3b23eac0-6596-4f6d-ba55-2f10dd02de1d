#import "Utils.h"
#import <UIKit/UIKit.h>
#import <CommonCrypto/CommonDigest.h>
#import <CommonCrypto/CommonHMAC.h>
#import <Foundation/Foundation.h>
#import <UserNotifications/UserNotifications.h>
#import <Social/Social.h>

@implementation Utils

// dict可以是字典或者字典数组
+(NSString *)dictToJson:(id)dict
{
    // 检查输入参数是否为nil，避免NSJSONSerialization崩溃
    if (dict == nil) {
        NSLog(@"dictToJson: input dict is nil, returning nil");
        return nil;
    }

    NSError *error = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:&error];
    NSString *jsonString = nil;

    if (jsonData == nil) {
        NSLog(@"dictToJson ：%@", [error localizedDescription]);
    } else {
        jsonString = [[NSString alloc]initWithData:jsonData encoding:NSUTF8StringEncoding];
    }

    return jsonString;
}

+(NSDictionary*) dictFromJson:(NSString*)str
{
    if (str == nil) {
        return nil;
    }
    NSError *error = nil;
    NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:[str dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers error:&error];
    if (error) {
        return nil;
    } else {
        return dict;
    }
}

// Adapted from https://auth0.com/docs/api-auth/tutorials/nonce#generate-a-cryptographically-random-nonce
+(NSString *)randomNonce:(NSInteger)length {
    NSAssert(length > 0, @"Expected nonce to have positive length");
    NSString *characterSet = @"0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._";
    NSMutableString *result = [NSMutableString string];
    NSInteger remainingLength = length;

    while (remainingLength > 0) {
        NSMutableArray *randoms = [NSMutableArray arrayWithCapacity:16];
        for (NSInteger i = 0; i < 16; i++) {
            uint8_t random = 0;
            int errorCode = SecRandomCopyBytes(kSecRandomDefault, 1, &random);
            NSAssert(errorCode == errSecSuccess, @"Unable to generate nonce: OSStatus %i", errorCode);
            
            [randoms addObject:@(random)];
        }

        for (NSNumber *random in randoms) {
            if (remainingLength == 0) {
                break;
            }

            if (random.unsignedIntValue < characterSet.length) {
                unichar character = [characterSet characterAtIndex:random.unsignedIntValue];
                [result appendFormat:@"%C", character];
                remainingLength--;
            }
        }
    }
    return [result copy];
}

+(NSString *)stringBySha256HashingString:(NSString *)input {
    const char *string = [input UTF8String];
    unsigned char result[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256(string, (CC_LONG)strlen(string), result);

    NSMutableString *hashed = [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (NSInteger i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [hashed appendFormat:@"%02x", result[i]];
    }
    return hashed;
}

+(NSString *)GetSystemLanguage
{
    @try {
        // 获取当前设备的首选语言
        NSString *language = [[NSLocale preferredLanguages] objectAtIndex:0];
        NSString *country = [[NSLocale currentLocale] objectForKey:NSLocaleCountryCode];
        
        // 初始化格式化后的语言
        NSString *formattedLanguage = language;
        
        // 处理简体和繁体中文的缩写
        if ([language containsString:@"hant"]) {
            formattedLanguage = @"TW";  // 繁体中文 (台湾)
        } else if ([language containsString:@"hans"]) {
            formattedLanguage = @"ZH";  // 简体中文
        }
        
        // 对特定的国家/语言进行映射
        if ([language isEqualToString:@"in"]) {
            formattedLanguage = @"ID";  // 印尼语
        } else if ([language isEqualToString:@"nb"]) {
            formattedLanguage = @"NO";  // 挪威语
        }
        
        // 格式化并返回语言信息，格式为：语言, 国家代码, 格式化语言代码
        NSString *result = [NSString stringWithFormat:@"%@,%@,%@", language, country, formattedLanguage];
        
        return result;
    }
    @catch (NSException *exception) {
        // 错误处理，输出日志
        NSLog(@"Error occurred while getting system language: %@", exception.reason);
        return @"";
    }
}

+(void) Vibrator
{
    UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleMedium];
    [generator prepare];
    [generator impactOccurred];
}

+(void) CopyToClipboard:(NSString*)text
{
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    [pasteboard setString:text];
}

+(void) OpenNotificationSettings
{
    NSURL *settingsUrl = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
    if ([[UIApplication sharedApplication] canOpenURL:settingsUrl]) {
        [[UIApplication sharedApplication] openURL:settingsUrl options:@{} completionHandler:nil];
    }
}

+(void) CheckNotificationEnabled
{
    // 使用 UNUserNotificationCenter 来检查通知设置
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    
    // 获取通知设置
    [center getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings *settings) {
        if (settings.authorizationStatus == UNAuthorizationStatusAuthorized || settings.authorizationStatus == UNAuthorizationStatusProvisional) {
            NSString *result = @"YES";
            UnitySendMessage("SdkWrapper", "Notification_Enable", [result UTF8String]);
        } else {
            NSString *result = @"NO";
            UnitySendMessage("SdkWrapper", "Notification_Enable", [result UTF8String]);
        }
    }];
}

+(void) InviteSystem:(NSString*)data
{
    @autoreleasepool {
        if (data != nil && data.length > 0) {
            NSDictionary *json = [Utils dictFromJson:data];

            if (json != nil) {
                NSString *desc = json[@"desc"];
                NSString *link = json[@"link"];
                NSString *title = json[@"title"];
                
                if (desc != nil && link != nil) {
                    // 创建分享内容
                    NSString *shareText = [NSString stringWithFormat:@"%@ %@", desc, link];
                    
                    // 使用 UIActivityViewController 来进行分享
                    UIActivityViewController *activityVC = [[UIActivityViewController alloc] initWithActivityItems:@[shareText] applicationActivities:nil];
                    
                    // 设置分享控制器的主题
                    if (title != nil) {
                        activityVC.excludedActivityTypes = @[UIActivityTypeMessage, UIActivityTypeMail];
                    }

                    // 获取当前的 view controller
                    UIViewController *topViewController = [UIApplication sharedApplication].keyWindow.rootViewController;

                    if ( [activityVC respondsToSelector:@selector(popoverPresentationController)] ) {
                        // iOS 8+
                        UIPopoverPresentationController *presentationController = [activityVC popoverPresentationController];
                        presentationController.sourceView = topViewController.view;
                        presentationController.sourceRect = CGRectMake(topViewController.view.bounds.size.width / 2.0, topViewController.view.bounds.size.height / 2.0, 1.0, 1.0);
                    }

                    // 在主线程中展示分享界面
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [topViewController presentViewController:activityVC animated:YES completion:nil];
                    });
                    
                    NSString *result = @"success";
                    UnitySendMessage("SdkWrapper", "Facebook_System_Invite_Success", [result UTF8String]);
                } else {
                    NSLog(@"SDK invite system error: Missing 'desc' or 'link' in data");
                    NSString *result = @"error";
                    UnitySendMessage("SdkWrapper", "Facebook_System_Invite_Fail", [result UTF8String]);
                }
            } else {
                NSLog(@"SDK invite system error: Failed to parse data");
                    NSString *result = @"error";
                    UnitySendMessage("SdkWrapper", "Facebook_System_Invite_Fail", [result UTF8String]);
            }
        } else {
            NSLog(@"SDK invite system error: data is empty");
            NSString *result = @"error";
            UnitySendMessage("SdkWrapper", "Facebook_System_Invite_Fail", [result UTF8String]);
        }
    }
}

+(long) GetAvailableSpace:(NSString*)path
{
    NSDictionary *fileAttributes = [[NSFileManager defaultManager] attributesOfFileSystemForPath:path error:nil];
    if (fileAttributes) {
        NSNumber *freeSize = [fileAttributes objectForKey:NSFileSystemFreeSize];
        return [freeSize longLongValue];
    }
    return -1; // 返回 -1 表示获取失败
}

+(long) GetTotalSpace:(NSString*)path
{
    NSDictionary *fileAttributes = [[NSFileManager defaultManager] attributesOfFileSystemForPath:path error:nil];
    if (fileAttributes) {
        NSNumber *totalSize = [fileAttributes objectForKey:NSFileSystemSize];
        return [totalSize longLongValue];
    }
    return -1; // 返回 -1 表示获取失败
}

+(long) GetUsedSpace:(NSString*)path
{
    NSDictionary *fileAttributes = [[NSFileManager defaultManager] attributesOfFileSystemForPath:path error:nil];
    if (fileAttributes) {
        NSNumber *freeSize = [fileAttributes objectForKey:NSFileSystemFreeSize];
        NSNumber *totalSize = [fileAttributes objectForKey:NSFileSystemSize];
        if (freeSize && totalSize) {
            return [totalSize longLongValue] - [freeSize longLongValue];
        }
    }
    return -1; // 返回 -1 表示获取失败
}

+(void) OpenFansUrl:(NSString*)data
{
    @try {
        NSDictionary *jsonDict = [Utils dictFromJson:data];
        if (jsonDict)
        {
            NSString *appFansUrl = jsonDict[@"appFansUrl"];
            NSString *webFansUrl = jsonDict[@"webFansUrl"];
            
            NSLog(@"openFansUrl: appFansUrl = %@", appFansUrl);
            
            if (appFansUrl && appFansUrl.length > 0) {
                // 尝试打开 appFansUrl
                NSURL *url = [NSURL URLWithString:appFansUrl];
                if ([[UIApplication sharedApplication] canOpenURL:url]) {
                    [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
                    return;
                }
            }
            
            // 如果 appFansUrl 不可用，尝试打开 webFansUrl
            if (webFansUrl && webFansUrl.length > 0) {
                NSURL *url = [NSURL URLWithString:webFansUrl];
                if ([[UIApplication sharedApplication] canOpenURL:url]) {
                    [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
                }
            }
        }
    }
    @catch (NSException *exception) {
        NSLog(@"openFansUrl 出现异常: %@", exception.reason);
    }
}

+(void) OpenUrl:(NSString*)url
{
    @try {
        NSURL *nsUrl = [NSURL URLWithString:url];
        if ([[UIApplication sharedApplication] canOpenURL:nsUrl]) {
            [[UIApplication sharedApplication] openURL:nsUrl options:@{} completionHandler:nil];
        }
    } @catch (NSException *exception) {
        NSLog(@"[Utils] Exception in OpenUrl: %@", exception.reason);
    }
}
@end
